{"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "supabase": {"command": "uvx", "args": ["mcp-server-supabase"], "env": {"SUPABASE_URL": "https://xetsvpfunazwkontdpdh.supabase.co", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhldHN2cGZ1bmF6d2tvbnRkcGRoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTgyMzAwMiwiZXhwIjoyMDU1Mzk5MDAyfQ.m40XgEXpqK613HUfx8kx-liXMGGMNBIus759th80wns"}, "disabled": false, "autoApprove": ["supabase_query", "supabase_insert", "supabase_update", "supabase_delete"]}}}